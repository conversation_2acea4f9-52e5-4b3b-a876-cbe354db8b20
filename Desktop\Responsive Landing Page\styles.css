/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Navigation Styles */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: background 0.3s ease, box-shadow 0.3s ease;
    padding: 1rem 0;
}

/* When scrolled - blue background */
.navbar.scrolled {
    background: #2563eb;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo a {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2563eb;
    text-decoration: none;
    transition: color 0.3s;
}

.navbar.scrolled .nav-logo a {
    color: white;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 1.5rem;
}

.nav-item {
    position: relative;
}

.nav-link {
    text-decoration: none;
    color: #374151;
    font-weight: 500;
    padding: 0.75rem 1.25rem;
    border-radius: 6px;
    transition: all 0.2s;
    position: relative;
}

.navbar.scrolled .nav-link {
    color: white;
}

/* Simple hover effect */
.nav-link:hover {
    color: white;
    background: #3b82f6;
    transform: translateY(-1px);
}

.navbar.scrolled .nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* Active link */
.nav-link.active {
    color: #2563eb;
    background: rgba(37, 99, 235, 0.1);
}

.navbar.scrolled .nav-link.active {
    color: white;
    background: rgba(255, 255, 255, 0.2);
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

.navbar.scrolled .bar {
    background: #fff;
}

/* Hero Section */
.hero {
    height: 100vh;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease 0.2s both;
}

.cta-button {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    background: transparent;
    color: white;
    border: 2px solid white;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: fadeInUp 1s ease 0.4s both;
}

.cta-button:hover {
    background: white;
    color: #2563eb;
    transform: translateY(-2px);
}

/* Sections */
.section {
    padding: 5rem 0;
    min-height: 80vh;
}

.section:nth-child(even) {
    background: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.section h2 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #1a237e;
}

.section p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
}

/* Portfolio Grid */
.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.portfolio-item {
    height: 200px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
    transition: transform 0.3s ease;
}

.portfolio-item:hover {
    transform: scale(1.05);
}

/* Contact Form */
.contact-form {
    max-width: 600px;
    margin: 0 auto;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.contact-form button {
    background: #667eea;
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.3s ease;
}

.contact-form button:hover {
    background: #5a6fd8;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(255, 255, 255, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        backdrop-filter: blur(10px);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .section {
        padding: 3rem 0;
    }
}
