/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Navigation Styles */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: background 0.3s ease, box-shadow 0.3s ease;
    padding: 1rem 0;
}

/* When scrolled - blue background */
.navbar.scrolled {
    background: #2563eb;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo a {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2563eb;
    text-decoration: none;
    transition: color 0.3s;
}

.navbar.scrolled .nav-logo a {
    color: white;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 1.5rem;
}

.nav-item {
    position: relative;
}

.nav-link {
    text-decoration: none;
    color: #374151;
    font-weight: 500;
    padding: 0.75rem 1.25rem;
    border-radius: 6px;
    transition: all 0.2s;
    position: relative;
}

.navbar.scrolled .nav-link {
    color: white;
}

/* Simple hover effect */
.nav-link:hover {
    color: white;
    background: #3b82f6;
    transform: translateY(-1px);
}

.navbar.scrolled .nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* Active link */
.nav-link.active {
    color: #2563eb;
    background: rgba(37, 99, 235, 0.1);
}

.navbar.scrolled .nav-link.active {
    color: white;
    background: rgba(255, 255, 255, 0.2);
}

/* Mobile menu button */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #374151;
    margin: 3px 0;
    transition: 0.3s;
}

.navbar.scrolled .bar {
    background: white;
}

/* Hero Section */
.hero {
    height: 100vh;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease 0.2s both;
}

.cta-button {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    background: transparent;
    color: white;
    border: 2px solid white;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: fadeInUp 1s ease 0.4s both;
}

.cta-button:hover {
    background: white;
    color: #2563eb;
    transform: translateY(-2px);
}

/* Sections */
.section {
    padding: 5rem 0;
    min-height: 80vh;
}

.section:nth-child(even) {
    background: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.section h2 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #2563eb;
}

.section p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
}

/* Values Grid */
.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.value-item {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.2s;
}

.value-item:hover {
    transform: translateY(-3px);
}

.value-item h3 {
    color: #2563eb;
    margin-bottom: 1rem;
}

/* Events Grid */
.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.event-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 1.5rem;
    transition: transform 0.2s;
}

.event-card:hover {
    transform: translateY(-3px);
}

.event-date {
    background: #2563eb;
    color: white;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    min-width: 80px;
}

.event-date .day {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
}

.event-date .month {
    display: block;
    font-size: 0.9rem;
    text-transform: uppercase;
}

.event-info h3 {
    color: #2563eb;
    margin-bottom: 0.5rem;
}

.event-time {
    color: #6b7280;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Membership Benefits */
.membership-benefits {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.benefit-item {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.benefit-item h3 {
    color: #2563eb;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.membership-cta {
    background: #f8fafc;
    padding: 3rem;
    border-radius: 12px;
    text-align: center;
    margin-top: 3rem;
}

.membership-cta h3 {
    color: #2563eb;
    margin-bottom: 1rem;
}

.join-button {
    background: #2563eb;
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: background 0.2s;
    margin-top: 1rem;
}

.join-button:hover {
    background: #1d4ed8;
}

/* Contact Info */
.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.contact-item {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.contact-item h3 {
    color: #2563eb;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

/* Contact Form */
.contact-form {
    max-width: 600px;
    margin: 3rem auto 0;
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.contact-form h3 {
    color: #2563eb;
    margin-bottom: 1.5rem;
    text-align: center;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.2s;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #2563eb;
}

.contact-form button {
    background: #2563eb;
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.2s;
    width: 100%;
}

.contact-form button:hover {
    background: #1d4ed8;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
/* Mobile styles */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }

    .nav-logo a {
        font-size: 1.3rem;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 0;
        flex-direction: column;
        background: white;
        width: 100%;
        height: 100vh;
        text-align: center;
        transition: left 0.3s ease;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        padding-top: 6rem;
        z-index: 999;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-item {
        margin: 0.5rem 0;
    }

    .nav-link {
        display: block;
        padding: 1rem 2rem;
        font-size: 1.1rem;
        color: #374151;
        border-radius: 0;
        margin: 0 2rem;
        border-bottom: 1px solid #f3f4f6;
        transition: all 0.2s ease;
    }

    .nav-link:hover {
        background: #f8fafc;
        color: #2563eb;
        transform: none;
    }

    .nav-link.active {
        background: #eff6ff;
        color: #2563eb;
        border-left: 4px solid #2563eb;
    }

    .hamburger {
        display: flex;
        z-index: 1001;
    }

    .hamburger.active {
        position: fixed;
        right: 1rem;
        top: 1.5rem;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
        background: #374151;
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
        background: #374151;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .section {
        padding: 3rem 0;
    }

    .events-grid {
        grid-template-columns: 1fr;
    }

    .event-card {
        flex-direction: column;
        text-align: center;
    }

    .event-date {
        align-self: center;
    }

    .membership-benefits {
        grid-template-columns: 1fr;
    }

    .contact-info {
        grid-template-columns: 1fr;
    }
}

/* Extra small screens (phones) */
@media (max-width: 480px) {
    .nav-container {
        padding: 0 0.5rem;
    }

    .nav-logo a {
        font-size: 1.1rem;
    }

    .nav-menu {
        padding-top: 5rem;
    }

    .nav-link {
        font-size: 1rem;
        padding: 0.8rem 1.5rem;
        margin: 0 1rem;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .section h2 {
        font-size: 2rem;
    }

    .container {
        padding: 0 1rem;
    }

    .cta-button, .join-button {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }
}
